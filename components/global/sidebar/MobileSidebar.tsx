"use client"
import { But<PERSON>, buttonVariants } from '@/components/ui/button'
import { Sheet, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from '@/components/ui/sheet'
import { SIDEBAR_MENU } from '@/constants/menu'
import { MenuIcon } from 'lucide-react'
import { usePathname } from 'next/navigation'
import React, { useState } from 'react'
import Logo from './Logo'
import Link from 'next/link'

type Props = {}

function MobileSidebar(props: Props) {
    const [isOpen, setOpen] = useState(false)
    const pathname = usePathname()
    const activeRoute = SIDEBAR_MENU.find(
        (route) => route.href.length > 0 && pathname.includes(route.href) || SIDEBAR_MENU[0]
    )
  return (
    <div className='block border-separate bg-background md:hidden'>
        <nav className='container flex items-center justify-between px-8'>
            <Sheet open={isOpen} onOpenChange={setOpen}>
                <SheetTrigger asChild>
                    <Button variant={"ghost"} size={"icon"}>
                        <MenuIcon />
                    </Button>
                </SheetTrigger>
                <SheetContent
                    className='w-[400px] sm:w-[540px] space-y-4'
                    side={"left"}
                >
                    <Logo />
                    <div className='flex flex-col gap-1'>
                        {SIDEBAR_MENU.map((route) => (
                            <Link
                                key={route.href}
                                href={route.href}
                                className={buttonVariants({
                                    variant: activeRoute?.href === route.href ? "sidebarActiveItem" : "sidebarItem"
                                })}
                                onClick={() => setOpen((prev) => !prev)}
                            >
                                {route.icon}
                                {route.label}
                            </Link>
                        ))}
                    </div>
                </SheetContent>
            </Sheet>
        </nav>
    </div>
  )
}

export default MobileSidebar