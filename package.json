{"name": "scrapeflow-step-by-step", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "5.3.2", "@hookform/resolvers": "^3.9.0", "@prisma/client": "^5.18.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@tanstack/react-query": "^5.52.0", "@types/winston": "^2.4.4", "@xyflow/react": "^12.8.2", "cheerio": "1.0.0-rc.12", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cron-parser": "^4.9.0", "cronstrue": "^2.50.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "embla-carousel-react": "^8.2.0", "input-otp": "^1.2.4", "lucide-react": "^0.424.0", "next": "14.2.5", "next-themes": "^0.4.6", "nextjs-toploader": "^1.6.12", "openai": "^4.56.0", "puppeteer": "^22.15.0", "react": "^18.3.1", "react-countup": "^6.5.3", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.52.2", "react-resizable-panels": "^2.1.1", "recharts": "^2.12.7", "sonner": "^1.5.0", "stripe": "^16.8.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.1", "winston": "^3.17.0", "zod": "^3.23.8"}, "devDependencies": {"@tanstack/react-query-devtools": "^5.52.0", "@types/node": "^20.16.1", "@types/react": "^18.3.4", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "browser-sync-webpack-plugin": "^2.3.0", "eslint": "^9.33.0", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^10.1.8", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.4.41", "prisma": "^5.18.0", "tailwindcss": "^3.4.10", "typescript": "^5.5.4"}}