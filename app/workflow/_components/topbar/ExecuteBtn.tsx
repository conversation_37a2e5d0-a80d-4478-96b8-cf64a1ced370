'use client';

import { RunWorkflow } from '@/actions/workflows/runWorkflow';
import { Button } from '@/components/ui/button';
import useExecutingPlan from '@/hooks/use-executing-plan';
import { useMutation } from '@tanstack/react-query';
import { useReactFlow } from '@xyflow/react';
import { PlayIcon } from 'lucide-react';
import { toast } from 'sonner';

type Props = {
  workflowId: string;
};

const ExecuteBtn = ({ workflowId }: Props) => {
  const generate = useExecutingPlan();
  const { toObject } = useReactFlow();

  const mutation = useMutation({
    mutationFn: RunWorkflow,
    onSuccess: () => {
      toast.success('Executing started', { id: 'flow-executing' });
    },
    onError: () => {
      toast.error('Executing failed', { id: 'flow-executing' });
    },
  });

  return (
    <Button
      variant={'outline'}
      className='flex items-center gap-2'
      disabled={mutation.isPending}
      onClick={() => {
        const plan = generate();
        if (!plan) {
          //Client side validation
          return;
        }

        mutation.mutate({
          workflowId,
          flowDefinition: JSON.stringify(toObject()),
        });
      }}
    >
      <PlayIcon size={16} className='stroke-orange-400' />
      Execute
    </Button>
  );
};

export default ExecuteBtn;
