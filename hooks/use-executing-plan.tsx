import {
  FlowToExecutingPlan,
  FlowToExecutingPlanValidationError,
} from '@/lib/workflow/executingPlan';
import { AppNode } from '@/types/appNode';
import { useReactFlow } from '@xyflow/react';
import { useCallback } from 'react';
import { toast } from 'sonner';
import useFlowValidation from './use-flow-validation';

const useExecutingPlan = () => {
  const { toObject } = useReactFlow();
  const { setInvalidInputs, clearErrors } = useFlowValidation();
  const handleError = useCallback(
    (error: any) => {
      switch (error.type) {
        case FlowToExecutingPlanValidationError.NO_ENTRY_POINT:
          toast.error('No entry point found');
          break;
        case FlowToExecutingPlanValidationError.INVALID_INPUTS:
          toast.error('Not all inputs values are set');
          setInvalidInputs(error.invalidElements);
          break;
        default:
          toast.error('something went wrong');
          break;
      }
    },
    [setInvalidInputs]
  );

  const generateExecutingPlan = useCallback(() => {
    const { nodes, edges } = toObject();
    const { executingPlan, error } = FlowToExecutingPlan(
      nodes as AppNode[],
      edges
    );

    if (error) {
      handleError(error);
      return null;
    }

    clearErrors();
    return executingPlan;
  }, [toObject, handleError, clearErrors]);

  return generateExecutingPlan;
};

export default useExecutingPlan;
