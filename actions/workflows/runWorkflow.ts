'use server';

import prisma from '@/lib/prisma';
import { ExecuteWorkflow } from '@/lib/workflow/executeWorkflow';
import { FlowToExecutingPlan } from '@/lib/workflow/executingPlan';
import { TaskRegistry } from '@/lib/workflow/task/registry';
import {
  ExecutionPhaseStatus,
  WorkflowExecutionPlan,
  WorkflowExecutionStatus,
  WorkflowExecutionTrigger,
} from '@/types/workflow';
import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';

export async function RunWorkflow(form: {
  workflowId: string;
  flowDefinition?: string;
}) {
  const { userId } = auth();

  if (!userId) {
    throw new Error('Unathenticated');
  }

  const { workflowId, flowDefinition } = form;

  if (!workflowId) {
    throw new Error('workflowId is required');
  }

  const workflow = await prisma.workflow.findUnique({
    where: {
      userId,
      id: workflowId,
    },
  });

  if (!workflow) {
    throw new Error('workflow not found');
  }

  let executingPlan: WorkflowExecutionPlan;

  if (!flowDefinition) {
    throw new Error('flow definition is not defined');
  }

  const flow = JSON.parse(flowDefinition);
  const result = FlowToExecutingPlan(flow.nodes, flow.edges);

  if (result.error) {
    throw new Error('flow definition is not valid');
  }

  if (!result.executingPlan) {
    throw new Error('no executing plan generated');
  }

  executingPlan = result.executingPlan;

  const execution = await prisma.workflowExecution.create({
    data: {
      workflowId,
      userId,
      status: WorkflowExecutionStatus.PENDING,
      startedAt: new Date(),
      trigger: WorkflowExecutionTrigger.MANUAL,
      definition: flowDefinition,
      phases: {
        create: executingPlan.flatMap(phase => {
          return phase.nodes.flatMap(node => {
            return {
              userId,
              status: ExecutionPhaseStatus.CREATED,
              number: phase.phase,
              node: JSON.stringify(node),
              name: TaskRegistry[node.data.type].label,
            };
          });
        }),
      },
    },
    select: {
      id: true,
      phases: true,
    },
  });

  if (!execution) {
    throw new Error('workflow execution not created');
  }

  // run this on background
  ExecuteWorkflow(execution.id);

  redirect(`/workflow/runs/${workflowId}/${execution.id}`);
}
