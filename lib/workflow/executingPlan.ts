import { logger } from '@/lib/logger';
import { AppNode, AppNodeMissingInputs } from '@/types/appNode';
import {
  WorkflowExecutionPlan,
  WorkflowExecutionPlanPhase,
} from '@/types/workflow';
import { Edge } from '@xyflow/react';
import { TaskRegistry } from './task/registry';

export enum FlowToExecutingPlanValidationError {
  'NO_ENTRY_POINT',
  'INVALID_INPUTS',
}

type FlowToExecutingPlanType = {
  executingPlan?: WorkflowExecutionPlan;
  error?: {
    type: FlowToExecutingPlanValidationError;
    invalidElements?: AppNodeMissingInputs[];
  };
};

export function FlowToExecutingPlan(
  nodes: AppNode[],
  edges: Edge[]
): FlowToExecutingPlanType {
  const startTime = Date.now();

  logger.workflow.plan('Starting workflow planning', {
    totalNodes: nodes.length,
    totalEdges: edges.length,
    nodeTypes: nodes.reduce((acc, node) => {
      acc[node.data.type] = (acc[node.data.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
  });

  // check entry point
  const entryPoint = nodes.find(
    node => TaskRegistry[node.data.type].isEntryPoint
  );

  if (!entryPoint) {
    logger.workflow.error('No entry point found in workflow', {
      availableNodes: nodes.map(n => ({ id: n.id, type: n.data.type })),
    });
    return {
      error: {
        type: FlowToExecutingPlanValidationError.NO_ENTRY_POINT,
      },
    };
  }

  logger.workflow.plan('Entry point found', {
    entryPointId: entryPoint.id,
    entryPointType: entryPoint.data.type,
  });

  const inputsWithErrors: AppNodeMissingInputs[] = [];
  const planned = new Set<string>();

  const invalidInputs = getInvalidInputs(entryPoint, edges, planned);
  if (invalidInputs.length > 0) {
    inputsWithErrors.push({
      nodeId: entryPoint.id,
      inputs: invalidInputs,
    });
  }

  const executingPlan: WorkflowExecutionPlan = [
    {
      phase: 1,
      nodes: [entryPoint],
    },
  ];

  planned.add(entryPoint.id);

  for (
    let phase = 2;
    phase <= nodes.length && planned.size < nodes.length;
    phase++
  ) {
    const phaseStartTime = Date.now();
    const nextPhase: WorkflowExecutionPlanPhase = { phase, nodes: [] };
    const initialPlannedSize = planned.size;

    logger.workflow.phase(phase, 'Starting phase planning', {
      currentlyPlanned: Array.from(planned),
      remainingNodes: nodes
        .filter(n => !planned.has(n.id))
        .map(n => ({
          id: n.id,
          type: n.data.type,
        })),
      progressPercentage: Math.round((planned.size / nodes.length) * 100),
    });

    for (const currentNode of nodes) {
      if (planned.has(currentNode.id)) {
        logger.workflow.node(currentNode.id, 'Already planned, skipping');
        continue;
      }

      logger.workflow.node(currentNode.id, 'Evaluating node for phase', {
        nodeType: currentNode.data.type,
        phase,
      });

      const invalidInputs = getInvalidInputs(currentNode, edges, planned);

      if (invalidInputs.length > 0) {
        const incomers = getIncomers(currentNode, nodes, edges);
        const allIncomersPlanned = incomers.every(incomer =>
          planned.has(incomer.id)
        );

        logger.workflow.node(currentNode.id, 'Has invalid inputs', {
          invalidInputs,
          incomers: incomers.map(inc => ({
            id: inc.id,
            planned: planned.has(inc.id),
          })),
          allIncomersPlanned,
        });

        if (allIncomersPlanned) {
          logger.workflow.error(
            'Invalid workflow: All dependencies satisfied but node still invalid',
            {
              nodeId: currentNode.id,
              nodeType: currentNode.data.type,
              invalidInputs,
              plannedIncomers: incomers.map(inc => inc.id),
              phase,
            }
          );

          inputsWithErrors.push({
            nodeId: currentNode.id,
            inputs: invalidInputs,
          });
        } else {
          logger.workflow.node(
            currentNode.id,
            'Waiting for dependencies, skipping'
          );
          continue;
        }
      }

      logger.workflow.node(currentNode.id, `✅ Adding to phase ${phase}`, {
        nodeType: currentNode.data.type,
      });
      nextPhase.nodes.push(currentNode);
    }

    if (nextPhase.nodes.length > 0) {
      for (const node of nextPhase.nodes) {
        planned.add(node.id);
      }
      executingPlan.push(nextPhase);

      const phaseTime = Date.now() - phaseStartTime;
      logger.workflow.phase(phase, 'Phase completed successfully', {
        nodesAdded: nextPhase.nodes.map(n => ({ id: n.id, type: n.data.type })),
        nodeCount: nextPhase.nodes.length,
        totalPlanned: planned.size,
        totalNodes: nodes.length,
        progressPercentage: Math.round((planned.size / nodes.length) * 100),
        phaseExecutionTime: `${phaseTime}ms`,
      });
    } else {
      logger.workflow.phase(phase, 'No nodes added to phase');
    }

    // Deadlock detection
    if (planned.size === initialPlannedSize && planned.size < nodes.length) {
      const unplannedNodes = nodes.filter(node => !planned.has(node.id));
      logger.workflow.error('Deadlock detected in workflow planning', {
        unplannedNodes: unplannedNodes.map(n => ({
          id: n.id,
          type: n.data.type,
        })),
        totalPlanned: planned.size,
        totalNodes: nodes.length,
        phase,
        possibleCauses: [
          'Circular dependencies between nodes',
          'Missing required inputs',
          'Invalid edge connections',
          'Required inputs not satisfied by any preceding node',
        ],
      });
      throw new Error(
        `Workflow contains circular dependencies or invalid connections. Unplanned nodes: ${unplannedNodes
          .map(n => n.id)
          .join(', ')}`
      );
    }
  }

  const totalTime = Date.now() - startTime;
  logger.workflow.plan('✅ Workflow planning completed successfully', {
    totalPhases: executingPlan.length,
    totalNodes: nodes.length,
    executionTime: `${totalTime}ms`,
    averageNodesPerPhase:
      Math.round((nodes.length / executingPlan.length) * 100) / 100,
    phaseSummary: executingPlan.map(phase => ({
      phase: phase.phase,
      nodeCount: phase.nodes.length,
      nodes: phase.nodes.map(n => ({ id: n.id, type: n.data.type })),
    })),
    performance: {
      totalExecutionTime: `${totalTime}ms`,
      averageTimePerNode: `${Math.round(totalTime / nodes.length)}ms`,
      planningEfficiency: `${Math.round(
        (nodes.length / totalTime) * 1000
      )} nodes/second`,
    },
  });

  if (inputsWithErrors.length > 0) {
    return {
      error: {
        type: FlowToExecutingPlanValidationError.INVALID_INPUTS,
        invalidElements: inputsWithErrors,
      },
    };
  }

  return { executingPlan };
}

function getInvalidInputs(node: AppNode, edges: Edge[], planned: Set<string>) {
  const invalidInputs = [];
  const inputs = TaskRegistry[node.data.type].inputs;

  logger.workflow.node(node.id, 'Validating node inputs', {
    totalInputs: inputs.length,
    inputDefinitions: inputs.map(inp => ({
      name: inp.name,
      required: inp.required,
      type: inp.type,
    })),
  });

  for (const input of inputs) {
    const inputValue = node.data.inputs[input.name];
    const inputValueProvided = inputValue?.length > 0;

    if (inputValueProvided) {
      logger.workflow.node(
        node.id,
        `Input '${input.name}' has user-provided value`,
        {
          inputName: input.name,
          valueLength: inputValue.length,
          required: input.required,
        }
      );
      continue;
    }

    // Check for linked outputs
    const incomingEdges = edges.filter(edge => edge.target === node.id);
    const inputLinkedToOutput = incomingEdges.find(
      edge => edge.targetHandle === input.name
    );

    const requiredInputProvidedByVisitedOutput =
      input.required &&
      inputLinkedToOutput &&
      planned.has(inputLinkedToOutput.source);

    if (requiredInputProvidedByVisitedOutput) {
      logger.workflow.node(
        node.id,
        `Required input '${input.name}' satisfied by planned output`,
        {
          inputName: input.name,
          sourceNode: inputLinkedToOutput.source,
          sourceHandle: inputLinkedToOutput.sourceHandle,
        }
      );
      continue;
    } else if (!input.required) {
      if (!inputLinkedToOutput) {
        logger.workflow.node(
          node.id,
          `Optional input '${input.name}' has no connection (valid)`,
          {
            inputName: input.name,
          }
        );
        continue;
      }
      if (inputLinkedToOutput && planned.has(inputLinkedToOutput.source)) {
        logger.workflow.node(
          node.id,
          `Optional input '${input.name}' satisfied by planned output`,
          {
            inputName: input.name,
            sourceNode: inputLinkedToOutput.source,
          }
        );
        continue;
      }
    }

    logger.workflow.node(node.id, `❌ Input '${input.name}' is invalid`, {
      inputName: input.name,
      required: input.required,
      hasConnection: !!inputLinkedToOutput,
      sourceNode: inputLinkedToOutput?.source,
      sourceNodePlanned: inputLinkedToOutput
        ? planned.has(inputLinkedToOutput.source)
        : false,
      reason: input.required
        ? 'Required input not satisfied by any planned node'
        : 'Optional input connected but source node not yet planned',
    });

    invalidInputs.push(input.name);
  }

  if (invalidInputs.length > 0) {
    logger.workflow.node(node.id, 'Node validation failed', {
      invalidInputCount: invalidInputs.length,
      invalidInputs,
      totalInputs: inputs.length,
      validInputs: inputs.length - invalidInputs.length,
    });
  }

  return invalidInputs;
}

function getIncomers(node: AppNode, nodes: AppNode[], edges: Edge[]) {
  if (!node.id) {
    return [];
  }

  const incomersIds = new Set();
  edges.forEach(edge => {
    if (edge.target === node.id) {
      incomersIds.add(edge.source);
    }
  });

  return nodes.filter(n => incomersIds.has(n.id));
}
