const isDev = process.env.NODE_ENV === 'development';
const isDevMode = process.env.NEXT_PUBLIC_DEV_MODE === 'true';
const shouldLog = (level: string) => level === 'error' || isDev || isDevMode;

// Simple timestamp
const getTimestamp = () => new Date().toLocaleTimeString();

// Core logger functions
const logWithFormat = (
  level: string,
  context: string,
  message: string,
  meta?: any
) => {
  if (!shouldLog(level)) return;

  const timestamp = getTimestamp();
  const prefix = `[${level.toUpperCase()}] ${timestamp} [${context}]`;
  const metaStr =
    meta && Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';

  const consoleMethod =
    level === 'debug'
      ? console.log
      : level === 'info'
      ? console.info
      : level === 'warn'
      ? console.warn
      : console.error;

  if (metaStr) {
    consoleMethod(`${prefix} ${message}\n${metaStr}`);
  } else {
    consoleMethod(`${prefix} ${message}`);
  }
};

// Main logger object
export const logger = {
  debug: (message: string, meta?: any, context = 'APP') => {
    logWithFormat('debug', context, message, meta);
  },

  info: (message: string, meta?: any, context = 'APP') => {
    logWithFormat('info', context, message, meta);
  },

  warn: (message: string, meta?: any, context = 'APP') => {
    logWithFormat('warn', context, message, meta);
  },

  error: (message: string, meta?: any, context = 'APP') => {
    logWithFormat('error', context, message, meta);
  },

  // Workflow methods
  workflow: {
    plan: (message: string, meta?: any) => {
      logWithFormat('debug', 'WORKFLOW-PLAN', message, meta);
    },

    node: (nodeId: string, message: string, meta?: any) => {
      logWithFormat('debug', 'NODE', `[${nodeId}] ${message}`, meta);
    },

    phase: (phase: number, message: string, meta?: any) => {
      logWithFormat('info', 'PHASE', `[Phase ${phase}] ${message}`, meta);
    },

    execution: (message: string, meta?: any) => {
      logWithFormat('info', 'WORKFLOW-EXEC', message, meta);
    },

    error: (message: string, meta?: any) => {
      logWithFormat('error', 'WORKFLOW-ERROR', message, meta);
    },
  },

  // API methods
  api: {
    request: (method: string, url: string, meta?: any) => {
      logWithFormat('info', 'API-REQ', `${method} ${url}`, meta);
    },

    response: (method: string, url: string, status: number, meta?: any) => {
      const level = status >= 400 ? 'error' : status >= 300 ? 'warn' : 'info';
      logWithFormat(level, 'API-RES', `${method} ${url} - ${status}`, meta);
    },

    error: (method: string, url: string, error: Error, meta?: any) => {
      logWithFormat('error', 'API-ERROR', `${method} ${url} - ERROR`, {
        ...meta,
        error: error.message,
        stack: error.stack,
      });
    },
  },
};

// Simple helpers
export const devLog = {
  log: (message: string, meta?: any) => {
    if (shouldLog('debug')) {
      console.log('🔍', message, meta || '');
    }
  },

  success: (message: string, meta?: any) => {
    if (shouldLog('info')) {
      console.log('✅', message, meta || '');
    }
  },

  error: (message: string, meta?: any) => {
    console.error('❌', message, meta || '');
  },

  warn: (message: string, meta?: any) => {
    if (shouldLog('warn')) {
      console.warn('⚠️', message, meta || '');
    }
  },
};

// Quick debug
export const d = (...args: any[]) => {
  if (shouldLog('debug')) {
    console.log('🐛', ...args);
  }
};

export default logger;
