"use client"

import React from 'react'
import Logo from './Logo'
import Link from 'next/link'
import { buttonVariants } from '@/components/ui/button'
import { usePathname } from 'next/navigation'
import { SIDEBAR_MENU } from '@/constants/menu'

type Props = {}


const DesktopSidebar = (props: Props) => {
    const pathname = usePathname()
    const activeRoute = SIDEBAR_MENU.find(
        (route) => route.href.length > 0 && pathname.includes(route.href) || SIDEBAR_MENU[0]
    )
  return (
    <div className="hidden relative md:block min-w-[280px] max-w-[280px] h-screen overflow-hidden w-full bg-primary/5 dark:bg-secondary/30 dark:text-foreground text-muted-foreground border-r-2 border-separate">
        <div className="flex items-center justify-center gap-2 border-b-[1px] border-separate p-4">
            <Logo/>
        </div>
        <div className="p-2">TODO CREDITS</div>
        <div className="flex flex-col p-2">
            {SIDEBAR_MENU.map((route) => (
                <Link
                    key={route.href}
                    href={route.href}
                    className={buttonVariants({
                        variant: activeRoute?.href === route.href ? "sidebarActiveItem" : "sidebarItem"
                    })}
                >
                    {route.icon}
                    {route.label}
                </Link>
            ))}
        </div>
    </div>
  )
}


export default DesktopSidebar