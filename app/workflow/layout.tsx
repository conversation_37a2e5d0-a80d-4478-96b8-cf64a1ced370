import Logo from '@/components/global/sidebar/Logo';
import ThemeModeToggle from '@/components/global/theme-mode-toggle';
import { Separator } from '@/components/ui/separator';

type Props = {
  children: React.ReactNode;
};

const Layout = ({ children }: Props) => {
  return (
    <div className='flex flex-col w-full h-screen'>
      {children}
      <Separator />
      <footer className='flex items-center justify-between p-2'>
        <Logo />
        <ThemeModeToggle />
      </footer>
    </div>
  );
};

export default Layout;
