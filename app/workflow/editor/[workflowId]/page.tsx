import Editor from '@/app/workflow/_components/Editor';
import prisma from '@/lib/prisma';
import { auth } from '@clerk/nextjs/server';

type Props = {};

const Page = async ({ params }: { params: { workflowId: string } }) => {
  const { workflowId } = params;
  const { userId } = auth();

  if (!userId) {
    return <div>Not authenticated</div>;
  }

  const workflow = await prisma.workflow.findUnique({
    where: {
      id: workflowId,
      userId,
    },
  });

  if (!workflow) {
    return <div>Workflow Not found</div>;
  }

  return <Editor workflow={workflow} />;
};

export default Page;
