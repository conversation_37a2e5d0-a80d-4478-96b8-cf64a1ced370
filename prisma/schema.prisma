// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Workflow {
  id          String  @id @default(cuid())
  userId      String
  name        String
  description String?
  definition  String
  status      String

  lastRunAt     DateTime?
  lastRunId     String?
  lastRunStatus String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  executions WorkflowExecution[]

  //User cannot have two workflows with the same name
  @@unique([userId, name])
}

model WorkflowExecution {
  id              String    @id @default(cuid())
  workflowId      String
  userId          String
  trigger         String
  creditsConsumed Int       @default(0)
  definition      String    @default("{}")
  status          String
  createdAt       DateTime  @default(now())
  startedAt       DateTime?
  completedAt     DateTime?

  workflow Workflow         @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  phases   ExecutionPhase[]
}

model ExecutionPhase {
  id                  String            @id @default(cuid())
  userId              String
  workflowExecutionId String
  name                String
  node                String
  number              Int
  inputs              String?
  outputs             String?
  status              String
  creditsConsumed     Int?
  startedAt           DateTime          @default(now())
  completedAt         DateTime?
  execution           WorkflowExecution @relation(fields: [workflowExecutionId], references: [id], onDelete: Cascade)
  logs                ExecutionLog[]
}

model ExecutionLog {
  id        String   @id @default(cuid())
  logLevel  String
  message   String
  timestamp DateTime @default(now())

  executionPhaseId String
  executionPhase   ExecutionPhase @relation(fields: [executionPhaseId], references: [id], onDelete: Cascade)
}
