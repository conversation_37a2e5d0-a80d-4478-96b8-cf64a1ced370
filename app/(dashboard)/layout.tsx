import BreadcrumbHeader from '@/components/global/breadcrumb/BreadcrumbHeader'
import DesktopSidebar from '@/components/global/sidebar/DesktopSidebar'
import MobileSidebar from '@/components/global/sidebar/MobileSidebar'
import ThemeModeToggle from '@/components/global/theme-mode-toggle'
import { Separator } from '@/components/ui/separator'
import { SignedIn, UserButton } from '@clerk/nextjs'
import React from 'react'

type Props = {
    children: React.ReactNode
}

const Layout = ({ children }: Props) => {
  return (
    <div className="flex h-screen">
        <MobileSidebar/>
        <DesktopSidebar/>
        <div className="flex flex-col flex-1 min-h-screen">
            <header className='flex items-center justify-between px-6 py-4 h-[50px] container'>
                <BreadcrumbHeader/>
                <div className='gap-1 flex items-center'>
                    <ThemeModeToggle/>
                    <SignedIn>
                        <UserButton/>
                    </SignedIn>
                </div>
            </header>
            <Separator />
            <div className="overflow-auto">
                <div className="flex-1 container py-4 text-accent-foreground">
                    {children}
                </div>
            </div>
        </div>
    </div>
  )
}

export default Layout