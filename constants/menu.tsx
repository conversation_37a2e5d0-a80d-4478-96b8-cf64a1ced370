import React from "react"
import { v4 as uuid } from 'uuid';
import { CoinsIcon, HomeIcon, Layers2Icon, ShieldCheckIcon } from 'lucide-react'

export type FieldProps = {
    id: string
    label: string
    href: string
}

type SideBarProps = {
    icon: React.ReactNode
} & FieldProps

export const SIDEBAR_MENU: SideBarProps[] = [
    {
        id: uuid(),
        href: "",
        label: "Home",
        icon: <HomeIcon size={20}/>
    },
    {
        id: uuid(),
        href: "workflows",
        label: "Workflows",
        icon: <Layers2Icon size={20}/>
    },
    {
        id: uuid(),
        href: "credentials",
        label: "Credentials",
        icon: <ShieldCheckIcon size={20}/>
    },
    {
        id: uuid(),
        href: "billing",
        label: "Billing",
        icon: <CoinsIcon size={20}/>
    }
]