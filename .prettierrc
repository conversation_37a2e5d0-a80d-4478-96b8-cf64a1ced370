{"semi": true, "singleQuote": true, "quoteProps": "as-needed", "trailingComma": "es5", "tabWidth": 2, "useTabs": false, "printWidth": 80, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "proseWrap": "preserve", "requirePragma": false, "vueIndentScriptAndStyle": false}