{
  // ===== FORMATTING & PRETTIER =====
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "always",
    "source.organizeImports": "always"
  },

  // ===== LANGUAGE SPECIFIC FORMATTERS =====
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },

  // ===== INDENTATION & SPACING =====
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,

  // ===== AUTO SAVE =====
  "files.autoSave": "onFocusChange",
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,

  // ===== EMMET SUPPORT =====
  "emmet.includeLanguages": {
    "javascript": "javascriptreact",
    "typescript": "typescriptreact"
  },
  "emmet.triggerExpansionOnTab": true,

  // ===== EDITOR APPEARANCE =====
  "editor.fontFamily": "Fira Code, Consolas, 'Courier New', monospace",
  "editor.fontLigatures": true,
  "editor.fontSize": 14,
  "editor.lineHeight": 1.6,
  "editor.cursorBlinking": "smooth",
  "editor.cursorSmoothCaretAnimation": "on",

  // ===== BRACKET & GUIDES =====
  "editor.guides.bracketPairs": "active",
  "editor.bracketPairColorization.enabled": true,
  "editor.matchBrackets": "always",

  // ===== SUGGESTIONS & INTELLISENSE =====
  "editor.suggest.insertMode": "replace",
  "editor.acceptSuggestionOnCommitCharacter": false,
  "editor.quickSuggestions": {
    "other": "on",
    "comments": "off",
    "strings": "on"
  },
  "editor.parameterHints.enabled": true,
  "editor.suggestOnTriggerCharacters": true,

  // ===== MINIMAP & UI =====
  "editor.minimap.enabled": false,
  "editor.minimap.size": "fit",
  "workbench.iconTheme": "material-icon-theme",
  "workbench.tree.indent": 20,

  // ===== TERMINAL =====
  "terminal.integrated.fontSize": 13,
  "terminal.integrated.fontFamily": "Fira Code",
  "terminal.integrated.defaultProfile.windows": "Git Bash",

  // ===== GIT =====
  "git.enableSmartCommit": true,
  "git.confirmSync": false,
  "git.autofetch": true,

  // ===== SEARCH & FILES =====
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.git": true
  },
  "files.exclude": {
    "**/.git": true,
    "**/.DS_Store": true,
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true
  },

  // ===== TYPESCRIPT & JAVASCRIPT =====
  "typescript.preferences.importModuleSpecifier": "relative",
  "javascript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "javascript.suggest.autoImports": true,

  // ===== PERFORMANCE =====
  "extensions.ignoreRecommendations": false,
  "telemetry.telemetryLevel": "off",
  "editor.semanticHighlighting.enabled": true
}
